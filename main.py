# -- coding: utf-8 --
from typing import List, Optional
from dection_action.main import second_extract_todo, split_content_and_request_todo
from extract_abstract.Schema import DialogItem, MeetingAbstractResultV2, MeetingAbstractContentV3, MeetingKeyPosition, MeetingKeyPhrase
import json
from extract_abstract.main import second_extract_abstract, split_content_and_request_abstract
from scene_recognition.main import detect_meeting_type
from utils.SplitUtils import split_paragraph
from common.GlobalConfig import g_detect_todo_use_new
import time

def merge(input):
    task_begin = time.time()  # 开始时间

    data = json.loads(input)
    dialog_id = data['dialog_id']
    paragraph = data['paragraph']
    
    speaker_info: Optional[dict] = {}
    focuses: Optional[List[str]] = []
    if "speaker_info" in data:
      speaker_info = data['speaker_info']
    if "focuses" in data:
      focuses = data["focuses"]

    #默认值
    merge_result = {
                    "dialog_id": dialog_id, 
                    "meeting_type": "其他",
                    "abstract": {"abstract": "该会议有效内容过短，没有明确的主题和信息，无法简洁概括。", "content": []},
                    "todo": {"action_items_list": [], "total_todos_list": []}
                   }

    dialog_items = []
    content_length = 0
    for index, input_dialog_item in enumerate(paragraph):
        if input_dialog_item['content']:
            dialog_items.append(DialogItem(index=index,
                                           timestamp=input_dialog_item['timestamp'],
                                           speaker=input_dialog_item['speaker'],
                                           content=input_dialog_item['content']))
            content_length += len(input_dialog_item['content'])
    if not dialog_items:
        return merge_result

    spans = split_paragraph(dialog_items)
    if not spans or len("".join([ut.content for ut in spans[0]])) == 0:
        return merge_result

    print('--merge_task begin--', len(spans))

    #耗时统计
    abstract_cost = 0
    scene_cost = 0
    todo_cost = 0

    scene_list = []
    #提取会议类型（内容太多只取第一个和最后一个span，避免内容太多prefill不够）
    # 支持手动指定meeting_type，如果提供且非空则直接使用，否则调用检测函数
    if data.get('meeting_type'):
        meeting_type = data.get('meeting_type')
        scene_cost = 0  # 手动指定时无检测成本
        print('==meeting_type manually provided==: ', meeting_type)
    else:
        begin_time = time.time()  # 开始时间
        meeting_type = detect_meeting_type(input)['meeting_type']
        scene_cost = int((time.time() - begin_time) * 1000)
        print('==detion_meeting_type cost==: ', scene_cost, meeting_type)
    merge_result["meeting_type"] = meeting_type

    #提取摘要和待办
    abstract_contents = []
    action_list = []
    for span in spans:
        begin_time = time.time()  # 开始时间
        #分段提取摘要
        item = split_content_and_request_abstract(span)
        if item:
            abstract_contents.append(item)
        abstract_cost += int((time.time() - begin_time) * 1000)

        #如果是企业/政府会议，继续提取待办
        # if g_detect_todo_use_new and (meeting_type == "企业会议记录" or meeting_type == "政府会议记录"):
        if g_detect_todo_use_new:
            begin_time = time.time()  # 开始时间
            item = split_content_and_request_todo(span, speaker_info)
            if item:
                action_list.append(item)
            todo_cost += int((time.time() - begin_time) * 1000)

    if abstract_contents and len(abstract_contents) > 0:
        #二次提取摘要（独立任务）
        begin_time = time.time()  # 开始时间
        abstract, abstract_list = second_extract_abstract(abstract_contents)
        abstract_cost += int((time.time() - begin_time) * 1000)
        merge_result["abstract"] = {"abstract": abstract, "content": abstract_list}
        print('==abstract cost==: ', abstract_cost)


    if g_detect_todo_use_new and action_list and len(action_list) > 0:
        #二次提取待办（独立任务）
        begin_time = time.time()  # 开始时间
        action_items_list, total_todos_list = second_extract_todo(paragraph, action_list)
        merge_result["todo"] = {"action_items_list": action_items_list, "total_todos_list": total_todos_list}
        todo_cost += int((time.time() - begin_time) * 1000)
        print('==dection_todo cost==: ', todo_cost)

    print(f"==merge_task finish. cost==: {int((time.time() - task_begin) * 1000)}, {merge_result}")

    return merge_result

















